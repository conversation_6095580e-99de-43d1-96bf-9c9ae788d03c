import {
  hashPassword,
  generateSalt,
  verifyPassword,
  generateToken,
  verifyToken,
  createHandler,
  transporter,
} from "@/lib/utils";
import { CustomRequest } from "@/lib/middleware";
import {
  RegisterSchema,
  LoginSchema,
  SendVerificationEmailSchema,
} from "./auth.validation";
import * as AuthService from "./auth.service"; // eslint-disable-line

export const Register = createHandler(
  async (req, res, next, { CustomError, ResponseHandler, HttpRes }) => {
    // validate request body
    const { username, email, password } = await RegisterSchema.validate(
      req.body,
      { abortEarly: false },
    );

    // check if user already exists
    const user = await AuthService.searchUser({ email, username });
    if (user) {
      throw new CustomError(
        HttpRes.status.BAD_REQUEST,
        HttpRes.message.BAD_REQUEST,
        HttpRes.details.BAD_REQUEST + ": User already exists",
      );
    }

    // hash password
    const salt = generateSalt();
    const hashedPassworrd = await hashPassword(password, salt);

    // create new user into db
    const newUser = await AuthService.createUser({
      username,
      email,
      salt,
      password: hashedPassworrd,
    });

    // create user profile
    await AuthService.createUserProfile(newUser.uid);

    // send verification email

    res
      .status(HttpRes.status.CREATED)
      .json(ResponseHandler.success(HttpRes.message.CREATED, newUser));
  },
);

export const Login = createHandler(
  async (req, res, next, { CustomError, ResponseHandler, HttpRes }) => {
    // do input validation
    const { password } = await LoginSchema.validate(req.body, {
      abortEarly: false,
    });

    // check if user exists
    const user = await AuthService.searchUser({ email: req.body.email });
    if (!user) {
      throw new CustomError(
        HttpRes.status.NOT_FOUND,
        HttpRes.message.NOT_FOUND,
        HttpRes.details.NOT_FOUND + ": User not found",
      );
    }

    // do authentication
    const isValid = await verifyPassword(password, user.salt, user.password);
    if (!isValid) {
      throw new CustomError(
        HttpRes.status.UNAUTHORIZED,
        HttpRes.message.UNAUTHORIZED,
        HttpRes.details.UNAUTHORIZED + ": Invalid credentials",
      );
    }

    // generate session token
    const token = generateToken({
      uid: user.uid,
      role: user.role,
    });

    // return response
    res
      .header("Authorization", `Bearer ${token}`)
      .status(HttpRes.status.OK)
      .json(
        ResponseHandler.success(HttpRes.message.OK, {
          uid: user.uid,
          username: user.username,
          email: user.email,
          role: user.role,
          verified: user.verified,
          active: user.active,
        }),
      );
  },
);

export const SendVerificationEmail = createHandler(
  async (req, res, next, { CustomError, ResponseHandler, HttpRes }) => {
    // input validation
    const { email } = await SendVerificationEmailSchema.validate(req.body, {
      abortEarly: false,
    });

    // generate verification token
    const token = generateToken(
      {
        email,
        uid: (req as CustomRequest).user.uid,
      },
      { expiresIn: "1m" },
    );

    // send email
    transporter.sendMail({
      from: "Admin <<EMAIL>>",
      to: email,
      subject: "Test Drive from Express App",
      template: "email/verify-email",
      context: {
        subject: "Verify your email",
        appName: "TokoTok",
        userName: "Ali",
        verifyUrl: `http://localhost:2000/api/auth/verify-email?token=${token}`,
        expiresIn: "24 hours",
        supportEmail: "<EMAIL>",
        year: new Date().getFullYear(),
      },
    });

    res
      .status(HttpRes.status.OK)
      .json(ResponseHandler.success(HttpRes.message.NO_CONTENT, null));
  },
);

export const VerifyEmail = createHandler(
  async (req, res, next, { CustomError, ResponseHandler, HttpRes }) => {
    // get token from query params
    const { token } = req.query;
    if (!token) {
      throw new CustomError(
        HttpRes.status.BAD_REQUEST,
        HttpRes.message.BAD_REQUEST,
        HttpRes.details.BAD_REQUEST,
      );
    }

    // verify token
    const { uid } = verifyToken(token as string);
    if (!uid) {
      throw new CustomError(
        HttpRes.status.BAD_REQUEST,
        HttpRes.message.BAD_REQUEST,
        HttpRes.details.BAD_REQUEST,
      );
    }

    // update user verified status
    await AuthService.updateUser(uid, { verified: true });

    res.status(HttpRes.status.REDIRECT).redirect("http://localhost:2000");
  },
);
